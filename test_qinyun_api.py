from openai import OpenAI

# 初始化客户端
client = OpenAI(
    base_url="https://api.qingyuntop.top/v1",
    api_key='sk-HV7fNKrBUR6BE6daJIEz1ultzLU0Gr5OwPvt7LDxFTyin1b0',
    timeout=120
)

# 创建聊天完成
response = client.chat.completions.create(
  model="gpt-3.5-turbo",
  messages=[
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Who won the world series in 2020?"},
    {"role": "assistant", "content": "The Los Angeles Dodgers won the World Series in 2020."},
    {"role": "user", "content": "Where was it played?"}
  ]
)
print(response.choices[0].message.content)
